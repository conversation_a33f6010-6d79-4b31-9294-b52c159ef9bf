"use client";

import Hero from "@/components/press/hero";
import ItemList from "@/components/ui/item-list";
import { pressItems } from "@/constants";
import { cancelFrame, frame } from "framer-motion";
import { LenisRef, ReactLenis } from "lenis/react";
import Head from "next/head";
import { useEffect, useRef } from "react";

export default function Press() {
  const lenisRef = useRef<LenisRef>(null);

  useEffect(() => {
    function update(data: { timestamp: number }) {
      const time = data.timestamp;
      lenisRef.current?.lenis?.raf(time);
    }

    frame.update(update, true);

    return () => cancelFrame(update);
  }, []);

  return (
    <>
      <Head>
        <title>Moshood Obatula | Press</title>
      </Head>
      <ReactLenis root options={{ autoRaf: false }} ref={lenisRef}>
        <Hero />
        <div className="mx-auto max-w-7xl p-8 px-4 pb-0">
          <ItemList items={pressItems} />
        </div>
      </ReactLenis>
    </>
  );
}
